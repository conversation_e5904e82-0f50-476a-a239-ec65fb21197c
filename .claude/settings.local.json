{"permissions": {"allow": ["<PERSON><PERSON>(chmod:*)", "Bash(git add:*)", "Bash(node:*)", "Bash(./start_dev_linux_with_url_access.sh:*)", "<PERSON><PERSON>(python test:*)", "<PERSON><PERSON>(python3:*)", "Bash(open http://localhost:5173)", "Bash(grep:*)", "WebFetch(domain:localhost)", "Bash(rg:*)", "Bash(npm run dev:*)", "<PERSON><PERSON>(curl:*)", "Bash(npm run build:*)", "mcp__ide__getDiagnostics", "Bash(ls:*)"], "deny": []}}