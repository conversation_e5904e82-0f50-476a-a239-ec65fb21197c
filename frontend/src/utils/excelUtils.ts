import * as XLSX from 'xlsx';
import { parseMarkdownTable as parseTable } from './tableUtils';

/**
 * Convert a markdown table to Excel file and trigger download
 * @param tableData The markdown table content
 * @param fileName The name of the file to download
 */
export const convertMarkdownTableToExcel = (tableData: string, fileName: string = 'conversion_table.xlsx'): void => {
  try {
    // Parse the markdown table using the imported function
    const parsedTable = parseTable(tableData);

    if (!parsedTable.isValid || !parsedTable.headers || !parsedTable.rows) {
      console.error('Invalid table data for Excel export');
      throw new Error('Invalid table format');
    }

    const { headers, rows } = parsedTable;

    // Create worksheet from rows data
    const worksheet = XLSX.utils.json_to_sheet(rows);

    // Create workbook
    const workbook = XLSX.utils.book_new();
    XLSX.utils.book_append_sheet(workbook, worksheet, 'Conversion');

    // Generate Excel file and trigger download
    XLSX.writeFile(workbook, fileName);
    console.log('Excel file downloaded successfully:', fileName);
  } catch (error) {
    console.error('Error converting table to Excel:', error);
    throw error; // Re-throw to allow caller to handle
  }
};

/**
 * Convert a JSON table to Excel file and trigger download
 * @param tableData The table data in JSON format
 * @param fileName The name of the file to download
 */
export const convertJsonTableToExcel = (tableData: any, fileName: string = 'conversion_table.xlsx'): void => {
  try {
    // Handle different table data formats
    let worksheetData;

    if (Array.isArray(tableData)) {
      // If it's already an array of objects, use it directly
      worksheetData = tableData;
    } else if (tableData && typeof tableData === 'object') {
      // If it's a single object, wrap it in an array
      worksheetData = [tableData];
    } else {
      throw new Error('Invalid table data format');
    }

    // Create worksheet from the table data
    const worksheet = XLSX.utils.json_to_sheet(worksheetData);

    // Create workbook
    const workbook = XLSX.utils.book_new();
    XLSX.utils.book_append_sheet(workbook, worksheet, 'Conversion');

    // Generate Excel file and trigger download
    XLSX.writeFile(workbook, fileName);
    console.log('Excel file downloaded successfully:', fileName);
  } catch (error) {
    console.error('Error converting JSON to Excel:', error);
    throw error; // Re-throw to allow caller to handle
  }
};

/**
 * Convert structured table data to Excel file and trigger download
 * @param tableData The structured table data with headers and rows
 * @param fileName The name of the file to download
 */
export const convertStructuredTableToExcel = (tableData: { headers: string[], rows: any[] }, fileName: string = 'conversion_table.xlsx'): void => {
  try {
    const { headers, rows } = tableData;

    if (!headers || !rows || headers.length === 0 || rows.length === 0) {
      throw new Error('Invalid structured table data');
    }

    // Create worksheet from rows data
    const worksheet = XLSX.utils.json_to_sheet(rows);

    // Create workbook
    const workbook = XLSX.utils.book_new();
    XLSX.utils.book_append_sheet(workbook, worksheet, 'Conversion');

    // Generate Excel file and trigger download
    XLSX.writeFile(workbook, fileName);
    console.log('Excel file downloaded successfully:', fileName);
  } catch (error) {
    console.error('Error converting structured table to Excel:', error);
    throw error; // Re-throw to allow caller to handle
  }
};
