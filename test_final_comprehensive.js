const { chromium } = require('playwright');

/**
 * Final Comprehensive MCP Test - All Features
 */

async function finalComprehensiveTest() {
    console.log('🎯 Final Comprehensive MCP Test');
    console.log('===============================');
    
    const browser = await chromium.launch({ headless: false });
    const context = await browser.newContext();
    const page = await context.newPage();
    
    const results = {
        userInteraction: { passed: 0, total: 4 },
        aiResponse: { passed: 0, total: 3 },
        tableFunction: { passed: 0, total: 3 },
        persistence: { passed: 0, total: 2 }
    };
    
    function logResult(category, test, passed) {
        const status = passed ? '✅' : '❌';
        console.log(`${status} ${test}`);
        if (passed) results[category].passed++;
        results[category].total++;
    }
    
    try {
        // Navigate to app
        await page.goto('http://localhost:5173');
        await page.waitForLoadState('networkidle');
        console.log('\n📋 Phase 1: Basic Functionality');
        console.log('-------------------------------');
        
        // Test 1: User Message Interaction
        console.log('\n🔹 Testing User Message Features...');
        await page.fill('input[type="text"], textarea', 'Convert 1 inch to mm');
        await page.click('button[type="submit"]');
        await page.waitForTimeout(3000);
        
        const userCopyExists = await page.locator('[data-testid="user-copy-button"]').count() > 0;
        const userLikeExists = await page.locator('[data-testid="user-like-button"]').count() > 0;
        const userReportExists = await page.locator('[data-testid="user-report-button"]').count() > 0;
        const userFeedbackExists = await page.locator('[data-testid="user-feedback-button"]').count() > 0;
        
        logResult('userInteraction', 'User Copy Button', userCopyExists);
        logResult('userInteraction', 'User Like Button', userLikeExists);
        logResult('userInteraction', 'User Report Button', userReportExists);
        logResult('userInteraction', 'User Feedback Button', userFeedbackExists);
        
        // Test 2: AI Response
        console.log('\n🔹 Testing AI Response Features...');
        await page.waitForTimeout(15000); // Wait for AI response
        
        const aiResponseExists = await page.locator('.MuiBox-root:has-text("25.4"), .MuiBox-root:has-text("mm")').count() > 0;
        logResult('aiResponse', 'AI Response Appears', aiResponseExists);
        
        if (aiResponseExists) {
            const aiCopyExists = await page.locator('[data-testid="ai-copy-button"]').count() > 0;
            const aiLikeExists = await page.locator('[data-testid="ai-like-button"]').count() > 0;
            
            logResult('aiResponse', 'AI Copy Button', aiCopyExists);
            logResult('aiResponse', 'AI Like Button', aiLikeExists);
        } else {
            logResult('aiResponse', 'AI Copy Button', false);
            logResult('aiResponse', 'AI Like Button', false);
        }
        
        // Test 3: Table Functionality
        console.log('\n🔹 Testing Table Functionality...');
        const tableTestData = `S/S 430 BA NO PI
.015(+/-.0015) X 2.343"(+/-.005) X COIL                                              7190#
.015(+/-.0015) X 2.406"(+/-.005) X COIL                                              8,061#`;
        
        await page.fill('input[type="text"], textarea', tableTestData);
        await page.click('button[type="submit"]');
        await page.waitForTimeout(20000); // Wait for table response
        
        const tableExists = await page.locator('table, .MuiTable-root').count() > 0;
        const copyExcelExists = await page.locator('button:has-text("Copy for Excel")').count() > 0;
        const hasConvertedData = await page.locator('div:has-text("mm")').count() > 5;
        
        logResult('tableFunction', 'Table Generated', tableExists);
        logResult('tableFunction', 'Copy for Excel Button', copyExcelExists);
        logResult('tableFunction', 'Contains Converted Data', hasConvertedData);
        
        // Test 4: Persistence
        console.log('\n🔹 Testing Persistence...');
        const messagesBefore = await page.locator('.MuiBox-root:has-text("Convert 1 inch to mm")').count();
        
        await page.reload();
        await page.waitForLoadState('networkidle');
        await page.waitForTimeout(3000);
        
        const messagesAfter = await page.locator('.MuiBox-root:has-text("Convert 1 inch to mm")').count();
        const tablesAfter = await page.locator('table, .MuiTable-root').count();
        
        logResult('persistence', 'Messages Persist', messagesAfter > 0);
        logResult('persistence', 'Tables Persist', tablesAfter > 0);
        
        // Final Results
        console.log('\n📊 Final Test Results Summary');
        console.log('=============================');
        
        const categories = [
            { name: 'User Interaction', key: 'userInteraction' },
            { name: 'AI Response', key: 'aiResponse' },
            { name: 'Table Function', key: 'tableFunction' },
            { name: 'Persistence', key: 'persistence' }
        ];
        
        let totalPassed = 0;
        let totalTests = 0;
        
        categories.forEach(cat => {
            const result = results[cat.key];
            const percentage = ((result.passed / result.total) * 100).toFixed(1);
            const status = result.passed === result.total ? '✅' : result.passed > 0 ? '⚠️' : '❌';
            
            console.log(`${status} ${cat.name}: ${result.passed}/${result.total} (${percentage}%)`);
            totalPassed += result.passed;
            totalTests += result.total;
        });
        
        const overallPercentage = ((totalPassed / totalTests) * 100).toFixed(1);
        console.log(`\n🎯 Overall Success Rate: ${totalPassed}/${totalTests} (${overallPercentage}%)`);
        
        if (overallPercentage >= 90) {
            console.log('🎉 EXCELLENT! All major features are working correctly!');
        } else if (overallPercentage >= 75) {
            console.log('✅ GOOD! Most features are working with minor issues.');
        } else {
            console.log('⚠️ Some features need attention.');
        }
        
        console.log('\n🔍 Browser open for manual verification. Press Ctrl+C to close.');
        await new Promise(() => {}); // Keep running
        
    } catch (error) {
        console.error('❌ Test error:', error);
    }
}

finalComprehensiveTest().catch(console.error);
