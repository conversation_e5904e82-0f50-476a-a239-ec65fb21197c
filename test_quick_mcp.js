const { chromium } = require('playwright');

/**
 * Quick MCP Test - Focus on key functionality
 */

async function quickTest() {
    console.log('🚀 Quick MCP Test - Key Features');
    console.log('================================');
    
    const browser = await chromium.launch({ headless: false });
    const context = await browser.newContext();
    const page = await context.newPage();
    
    try {
        // Navigate to app
        await page.goto('http://localhost:5173');
        await page.waitForLoadState('networkidle');
        console.log('✅ App loaded');
        
        // Send a message
        const inputSelector = 'input[type="text"], textarea';
        await page.fill(inputSelector, 'Convert 1 inch to mm');
        await page.click('button[type="submit"]');
        console.log('✅ Message sent');
        
        // Wait for user message to appear
        await page.waitForTimeout(2000);
        
        // Check user message action buttons
        const userCopyExists = await page.locator('[data-testid="user-copy-button"]').count() > 0;
        const userLikeExists = await page.locator('[data-testid="user-like-button"]').count() > 0;
        console.log(`✅ User buttons: Copy=${userCopyExists}, Like=${userLikeExists}`);
        
        // Wait for AI response (longer timeout)
        console.log('⏳ Waiting for AI response...');
        await page.waitForTimeout(15000);
        
        // Check for AI response
        const aiResponseExists = await page.locator('.MuiBox-root:has-text("25.4"), .MuiBox-root:has-text("mm")').count() > 0;
        console.log(`AI Response Present: ${aiResponseExists}`);
        
        if (aiResponseExists) {
            // Check AI response buttons
            const aiCopyExists = await page.locator('[data-testid="ai-copy-button"]').count() > 0;
            const aiLikeExists = await page.locator('[data-testid="ai-like-button"]').count() > 0;
            console.log(`✅ AI buttons: Copy=${aiCopyExists}, Like=${aiLikeExists}`);
        }
        
        // Test persistence
        console.log('🔄 Testing persistence...');
        const messagesBefore = await page.locator('.MuiBox-root:has-text("Convert 1 inch to mm")').count();
        
        await page.reload();
        await page.waitForLoadState('networkidle');
        await page.waitForTimeout(3000);
        
        const messagesAfter = await page.locator('.MuiBox-root:has-text("Convert 1 inch to mm")').count();
        console.log(`✅ Persistence: ${messagesBefore} → ${messagesAfter} messages`);
        
        // Summary
        console.log('\n📊 Quick Test Results:');
        console.log(`User Message Buttons: ${userCopyExists && userLikeExists ? '✅' : '❌'}`);
        console.log(`AI Response: ${aiResponseExists ? '✅' : '❌'}`);
        console.log(`Message Persistence: ${messagesAfter > 0 ? '✅' : '❌'}`);
        
        // Keep browser open for manual inspection
        console.log('\n🔍 Browser open for manual verification. Press Ctrl+C to close.');
        await new Promise(() => {}); // Keep running
        
    } catch (error) {
        console.error('❌ Test error:', error);
    } finally {
        // Don't close browser automatically
    }
}

quickTest().catch(console.error);
