const { chromium } = require('playwright');

async function testTableDownload() {
    console.log('🚀 Testing Table Download Functionality');
    console.log('=====================================\n');

    const browser = await chromium.launch({ 
        headless: false,
        slowMo: 1000
    });
    
    const context = await browser.newContext();
    const page = await context.newPage();

    try {
        // Navigate to the application
        console.log('📋 Navigating to application...');
        await page.goto('http://localhost:3000');
        await page.waitForTimeout(3000);

        // Wait for the application to load
        await page.waitForSelector('input[placeholder*=""]', { timeout: 10000 });
        console.log('✅ Application loaded');

        // Test table generation
        console.log('\n📊 Testing table generation...');
        const tableTestData = `S/S 430 BA NO PI
.015(+/-.0015) X 2.343"(+/-.005) X COIL                                              7190#
.015(+/-.0015) X 2.406"(+/-.005) X COIL                                              8,061#
.015(+/-.0015) X 16.50"(+/-.005) X COIL                                              12,550#`;

        // Click table function
        const tableButton = page.locator('text=制表功能');
        if (await tableButton.count() > 0) {
            await tableButton.click();
            console.log('✅ Table function selected');
        }

        // Send table test data
        await page.fill('input[placeholder*=""]', tableTestData);
        await page.press('input[placeholder*=""]', 'Enter');
        console.log('✅ Table test data sent');

        // Wait for response
        await page.waitForTimeout(8000);

        // Check for table elements
        const tableExists = await page.locator('table, .MuiTable-root, [data-testid="table"]').count() > 0;
        console.log(`📊 Table generated: ${tableExists ? '✅' : '❌'}`);

        if (tableExists) {
            // Check for download button
            const downloadButton = page.locator('button:has-text("下载Excel"), button:has-text("Download Excel")');
            const downloadExists = await downloadButton.count() > 0;
            console.log(`📥 Download Excel button found: ${downloadExists ? '✅' : '❌'}`);

            if (downloadExists) {
                // Set up download listener
                const downloadPromise = page.waitForEvent('download');
                
                // Click download button
                await downloadButton.first().click();
                console.log('🔄 Download button clicked');

                try {
                    // Wait for download to start
                    const download = await downloadPromise;
                    console.log('✅ Download started successfully');
                    console.log(`📁 File name: ${download.suggestedFilename()}`);
                    
                    // Save the file to verify it's valid
                    await download.saveAs(`./test_downloads/${download.suggestedFilename()}`);
                    console.log('✅ File saved successfully');
                } catch (downloadError) {
                    console.log('❌ Download failed:', downloadError.message);
                }
            }

            // Test copy for Excel functionality
            console.log('\n📋 Testing Copy for Excel...');
            const copyButton = page.locator('button:has-text("Copy for Excel"), button:has-text("复制")');
            const copyExists = await copyButton.count() > 0;
            console.log(`📋 Copy for Excel button found: ${copyExists ? '✅' : '❌'}`);

            if (copyExists) {
                await copyButton.first().click();
                console.log('✅ Copy for Excel button clicked');
                await page.waitForTimeout(1000);
                
                // Try to get clipboard content (this might not work in all browsers)
                try {
                    const clipboardText = await page.evaluate(() => navigator.clipboard.readText());
                    if (clipboardText && clipboardText.includes('\t')) {
                        console.log('✅ Clipboard contains tab-separated data (TSV format)');
                        console.log(`📊 Sample: ${clipboardText.substring(0, 100)}...`);
                    } else {
                        console.log('⚠️  Clipboard content format unclear');
                    }
                } catch (clipError) {
                    console.log('⚠️  Could not read clipboard (browser security)');
                }
            }
        }

        console.log('\n📊 Test Summary');
        console.log('===============');
        console.log(`Table Generation: ${tableExists ? '✅ PASS' : '❌ FAIL'}`);
        console.log('Download and Copy functionality tested');

    } catch (error) {
        console.error('❌ Test failed:', error);
    }

    console.log('\n🔍 Browser will remain open for manual verification...');
    console.log('Press Ctrl+C to close when done.');
    
    // Keep browser open for manual verification
    await new Promise(() => {});
}

testTableDownload().catch(console.error);
