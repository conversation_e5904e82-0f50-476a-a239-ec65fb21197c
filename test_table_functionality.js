const { chromium } = require('playwright');

/**
 * Test Table Functionality Specifically
 */

async function testTableFunctionality() {
    console.log('📊 Testing Table Functionality');
    console.log('==============================');
    
    const browser = await chromium.launch({ headless: false });
    const context = await browser.newContext();
    const page = await context.newPage();
    
    try {
        // Navigate to app
        await page.goto('http://localhost:5173');
        await page.waitForLoadState('networkidle');
        console.log('✅ App loaded');
        
        // Test data from testCase.txt
        const tableTestData = `S/S 430 BA NO PI
.015(+/-.0015) X 2.343"(+/-.005) X COIL                                              7190#
.015(+/-.0015) X 2.406"(+/-.005) X COIL                                              8,061#
.015(+/-.0015) X 16.50"(+/-.005) X COIL                                              12,550#`;
        
        // Send table test data
        const inputSelector = 'input[type="text"], textarea';
        await page.fill(inputSelector, tableTestData);
        await page.click('button[type="submit"]');
        console.log('✅ Table test data sent');
        
        // Wait for response (longer timeout for table processing)
        console.log('⏳ Waiting for table response...');
        await page.waitForTimeout(20000);
        
        // Check for various table indicators
        const tableSelectors = [
            'table',
            '.MuiTable-root',
            '[data-testid="table"]',
            'div:has-text("Copy for Excel")',
            'button:has-text("Copy for Excel")',
            'div:has-text("| ")', // Markdown table indicator
            'div:has-text("mm")', // Should contain converted units
            'div:has-text("0.38")', // Should contain converted thickness
        ];
        
        console.log('🔍 Checking for table elements...');
        for (const selector of tableSelectors) {
            const count = await page.locator(selector).count();
            console.log(`  ${selector}: ${count > 0 ? '✅' : '❌'} (${count} found)`);
        }
        
        // Check for any response content
        const responseContent = await page.locator('.MuiBox-root').allTextContents();
        const hasTableContent = responseContent.some(content => 
            content.includes('mm') || 
            content.includes('|') || 
            content.includes('0.38') ||
            content.includes('table')
        );
        
        console.log(`📝 Response contains table content: ${hasTableContent ? '✅' : '❌'}`);
        
        if (hasTableContent) {
            // Look for copy button
            const copyButtons = await page.locator('button:has-text("Copy"), button:has-text("复制")').count();
            console.log(`📋 Copy buttons found: ${copyButtons}`);
            
            // Try to find and click copy for Excel button
            const excelCopyButton = page.locator('button:has-text("Copy for Excel"), button:has-text("复制到Excel")');
            const excelCopyExists = await excelCopyButton.count() > 0;
            
            if (excelCopyExists) {
                console.log('✅ Copy for Excel button found');
                await excelCopyButton.first().click();
                console.log('✅ Copy for Excel button clicked');
            } else {
                console.log('❌ Copy for Excel button not found');
            }
        }
        
        // Print some response content for debugging
        console.log('\n📄 Sample response content:');
        const allContent = await page.locator('.MuiBox-root').allTextContents();
        allContent.slice(-3).forEach((content, index) => {
            if (content.trim()) {
                console.log(`  ${index + 1}: ${content.substring(0, 100)}...`);
            }
        });
        
        console.log('\n🔍 Browser open for manual verification. Press Ctrl+C to close.');
        await new Promise(() => {}); // Keep running
        
    } catch (error) {
        console.error('❌ Test error:', error);
    }
}

testTableFunctionality().catch(console.error);
